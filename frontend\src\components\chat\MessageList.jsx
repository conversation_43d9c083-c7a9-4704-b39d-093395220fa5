import React, { useEffect, useRef, useCallback, useState } from 'react';
import { FixedSizeList as List } from 'react-window';
import { formatDistanceToNow, format, isToday, isYesterday } from 'date-fns';
import { Check, CheckCheck, Reply, MoreVertical } from 'lucide-react';
import LoadingSpinner from '../common/LoadingSpinner';

const MessageList = ({
  messages = [],
  isLoading = false,
  hasNextPage = false,
  loadMoreMessages,
  onMarkAsSeen,
  currentUserId,
  onReplyToMessage = null
}) => {
  const listRef = useRef(null);
  const containerRef = useRef(null);
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const [selectedMessage, setSelectedMessage] = useState(null);

  // Auto-scroll to bottom for new messages
  useEffect(() => {
    if (messages.length > 0 && listRef.current) {
      const lastMessage = messages[messages.length - 1];
      if (lastMessage.senderId === currentUserId || lastMessage.isOptimistic) {
        listRef.current.scrollToItem(messages.length - 1, 'end');
      }
    }
  }, [messages.length, currentUserId]);

  // Mark messages as seen when they come into view - debounced to prevent infinite loops
  useEffect(() => {
    if (messages.length === 0 || !onMarkAsSeen || !currentUserId) return;

    const timeoutId = setTimeout(() => {
      const unseenMessages = messages.filter(
        msg => msg.senderId !== currentUserId &&
        !msg.status?.seen?.some(s => s.userId === currentUserId)
      );

      if (unseenMessages.length > 0) {
        const messageIds = unseenMessages.map(msg => msg._id).filter(Boolean);
        if (messageIds.length > 0) {
          onMarkAsSeen(messageIds);
        }
      }
    }, 500); // Debounce by 500ms

    return () => clearTimeout(timeoutId);
  }, [messages.length, currentUserId, onMarkAsSeen]); // Only depend on messages.length, not the entire messages array

  // Handle scroll events
  const handleScroll = useCallback(({ scrollOffset, scrollDirection }) => {
    const container = containerRef.current;
    if (!container) return;

    const { scrollHeight, clientHeight } = container;
    const isNearBottom = scrollHeight - scrollOffset - clientHeight < 100;
    
    setShowScrollToBottom(!isNearBottom);

    // Load more messages when scrolling up near the top
    if (scrollDirection === 'backward' && scrollOffset < 100 && hasNextPage && !isLoading) {
      loadMoreMessages?.();
    }
  }, [hasNextPage, isLoading, loadMoreMessages]);

  // Scroll to bottom function
  const scrollToBottom = () => {
    if (listRef.current && messages.length > 0) {
      listRef.current.scrollToItem(messages.length - 1, 'end');
    }
  };

  // Format message timestamp
  const formatMessageTime = (timestamp) => {
    const date = new Date(timestamp);
    
    if (isToday(date)) {
      return format(date, 'HH:mm');
    } else if (isYesterday(date)) {
      return `Yesterday ${format(date, 'HH:mm')}`;
    } else {
      return format(date, 'MMM dd, HH:mm');
    }
  };

  // Get message status icon
  const getStatusIcon = (message) => {
    if (message.senderId !== currentUserId) return null;
    
    const { status } = message;
    
    if (status?.seen?.length > 0) {
      return <CheckCheck className="w-4 h-4 text-blue-400" />;
    } else if (status?.delivered?.length > 0) {
      return <CheckCheck className="w-4 h-4 text-whatsapp-gray" />;
    } else {
      return <Check className="w-4 h-4 text-whatsapp-gray" />;
    }
  };

  // Message component
  const MessageItem = ({ index, style }) => {
    const message = messages[index];
    const isOwn = message.senderId === currentUserId;
    const showAvatar = !isOwn && (index === 0 || messages[index - 1].senderId !== message.senderId);
    
    return (
      <div style={style} className="px-4 py-1">
        <div className={`flex ${isOwn ? 'justify-end' : 'justify-start'} items-end space-x-2`}>
          {/* Avatar for received messages */}
          {!isOwn && (
            <div className="w-8 h-8 flex-shrink-0">
              {showAvatar && (
                <img
                  src={message.senderAvatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(message.senderName || 'User')}&background=00a884&color=fff`}
                  alt={message.senderName}
                  className="w-8 h-8 rounded-full object-cover"
                />
              )}
            </div>
          )}

          {/* Message bubble */}
          <div
            className={`message-bubble ${isOwn ? 'sent' : 'received'} ${
              message.isOptimistic ? 'opacity-70' : ''
            } relative group`}
          >
            {/* Reply indicator */}
            {message.replyTo && (
              <div className="mb-2 p-2 bg-black bg-opacity-20 rounded border-l-2 border-whatsapp-primary">
                <p className="text-whatsapp-primary text-xs font-medium">
                  {message.replyTo.senderName}
                </p>
                <p className="text-xs opacity-80 truncate">
                  {message.replyTo.text}
                </p>
              </div>
            )}

            {/* Sender name for group chats */}
            {!isOwn && showAvatar && (
              <p className="text-whatsapp-primary text-xs font-medium mb-1">
                {message.senderName}
              </p>
            )}

            {/* Message text */}
            <p className="text-sm whitespace-pre-wrap break-words">
              {message.text}
            </p>

            {/* Message footer */}
            <div className="flex items-center justify-end mt-1 space-x-1">
              <span className="text-xs opacity-70">
                {formatMessageTime(message.createdAt)}
              </span>
              {getStatusIcon(message)}
            </div>

            {/* Message actions */}
            <div className="absolute top-0 right-0 opacity-0 group-hover:opacity-100 transition-opacity">
              <div className="flex items-center space-x-1 bg-whatsapp-dark rounded-lg p-1">
                {onReplyToMessage && (
                  <button
                    onClick={() => onReplyToMessage(message)}
                    className="p-1 text-whatsapp-gray hover:text-white transition-colors"
                    title="Reply"
                  >
                    <Reply className="w-4 h-4" />
                  </button>
                )}
                <button
                  onClick={() => setSelectedMessage(message)}
                  className="p-1 text-whatsapp-gray hover:text-white transition-colors"
                  title="More options"
                >
                  <MoreVertical className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (isLoading && messages.length === 0) {
    return (
      <div className="h-full flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="h-full relative" ref={containerRef}>
      {/* Loading indicator for pagination */}
      {isLoading && messages.length > 0 && (
        <div className="absolute top-4 left-1/2 transform -translate-x-1/2 z-10">
          <div className="bg-whatsapp-dark rounded-full p-2">
            <LoadingSpinner size="sm" />
          </div>
        </div>
      )}

      {/* Messages list */}
      {messages.length > 0 ? (
        <List
          ref={listRef}
          height={containerRef.current?.clientHeight || 400}
          itemCount={messages.length}
          itemSize={80} // Approximate height per message
          onScroll={handleScroll}
          className="scrollbar-thin"
        >
          {MessageItem}
        </List>
      ) : (
        <div className="h-full flex items-center justify-center">
          <div className="text-center text-whatsapp-gray">
            <p className="text-lg mb-2">No messages yet</p>
            <p className="text-sm">Start a conversation!</p>
          </div>
        </div>
      )}

      {/* Scroll to bottom button */}
      {showScrollToBottom && (
        <button
          onClick={scrollToBottom}
          className="absolute bottom-4 right-4 bg-whatsapp-primary hover:bg-whatsapp-secondary text-white p-3 rounded-full shadow-lg transition-all duration-200 z-10"
          title="Scroll to bottom"
        >
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </button>
      )}
    </div>
  );
};

export default MessageList;

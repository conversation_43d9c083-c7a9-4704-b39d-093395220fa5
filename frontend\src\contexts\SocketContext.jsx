import React, { createContext, useContext, useEffect, useRef, useState, useCallback } from 'react';
import { io } from 'socket.io-client';
import { useAuth } from './AuthContext';
import toast from 'react-hot-toast';

const SocketContext = createContext({});

export const useSocket = () => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};

export const SocketProvider = ({ children }) => {
  const { authToken, user, isAuthenticated } = useAuth();
  const socketRef = useRef(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [onlineUsers, setOnlineUsers] = useState(new Set());
  const [typingUsers, setTypingUsers] = useState(new Map()); // chatId -> Set of userIds

  // Event listeners storage
  const eventListenersRef = useRef(new Map());
  const reconnectTimeoutRef = useRef(null);

  // Queue for pending operations while connecting
  const pendingOperationsRef = useRef([]);
  const pendingListenersRef = useRef([]);

  // Initialize socket connection
  useEffect(() => {
    if (!isAuthenticated || !authToken || !user?.uid) {
      // Clean up existing connection if user is not authenticated
      if (socketRef.current) {
        console.log('🧹 Cleaning up socket connection - user not authenticated');
        socketRef.current.disconnect();
        socketRef.current = null;
        setIsConnected(false);
        setIsConnecting(false);
        setOnlineUsers(new Set());
        setTypingUsers(new Map());
        eventListenersRef.current.clear();
        pendingOperationsRef.current = [];
        pendingListenersRef.current = [];
      }
      return;
    }

    // Prevent multiple connections
    if (socketRef.current?.connected) {
      return;
    }

    // Prevent multiple connection attempts
    if (isConnecting) {
      return;
    }

    console.log('🔌 Initializing socket connection for user:', user.uid);
    setIsConnecting(true);

    // Create socket connection
    const socket = io(import.meta.env.VITE_SOCKET_URL || 'http://localhost:5000', {
      auth: {
        token: authToken
      },
      transports: ['websocket', 'polling'],
      timeout: 10000,
      forceNew: false,
      reconnection: true,
      reconnectionDelay: 1000,
      reconnectionAttempts: 5,
    });

    socketRef.current = socket;

    // Connection event handlers
    socket.on('connect', () => {
      console.log('✅ Socket connected:', socket.id);
      setIsConnected(true);
      setIsConnecting(false);

      // Clear any pending reconnect timeout
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }

      // Process pending event listeners
      const pendingListeners = [...pendingListenersRef.current];
      pendingListenersRef.current = [];
      pendingListeners.forEach(({ event, callback, resolve }) => {
        const cleanup = addEventListenerInternal(event, callback);
        resolve(cleanup);
      });

      // Process pending operations
      const pendingOps = [...pendingOperationsRef.current];
      pendingOperationsRef.current = [];
      pendingOps.forEach(({ operation, resolve }) => {
        const result = operation();
        resolve(result);
      });

      // Only show toast on first connection, not reconnections
      if (!socket.recovered) {
        toast.success('Connected to chat server');
      }
    });

    socket.on('disconnect', (reason) => {
      console.log('🔌 Socket disconnected:', reason);
      setIsConnected(false);
      setIsConnecting(false);

      if (reason === 'io server disconnect') {
        // Server disconnected the socket, try to reconnect after a delay
        reconnectTimeoutRef.current = setTimeout(() => {
          if (socketRef.current && !socketRef.current.connected) {
            setIsConnecting(true);
            socket.connect();
          }
        }, 2000);
      }
    });

    socket.on('connect_error', (error) => {
      console.error('❌ Socket connection error:', error);
      setIsConnected(false);
      setIsConnecting(false);

      if (error.message.includes('Authentication failed')) {
        toast.error('Authentication failed. Please sign in again.');
      }
    });

    // User status events
    socket.on('user:status', (data) => {
      const { userId, isOnline } = data;
      setOnlineUsers(prev => {
        const newSet = new Set(prev);
        if (isOnline) {
          newSet.add(userId);
        } else {
          newSet.delete(userId);
        }
        return newSet;
      });
    });

    // Typing events
    socket.on('typing:start', (data) => {
      const { chatId, userId } = data;
      setTypingUsers(prev => {
        const newMap = new Map(prev);
        if (!newMap.has(chatId)) {
          newMap.set(chatId, new Set());
        }
        newMap.get(chatId).add(userId);
        return newMap;
      });
    });

    socket.on('typing:stop', (data) => {
      const { chatId, userId } = data;
      setTypingUsers(prev => {
        const newMap = new Map(prev);
        if (newMap.has(chatId)) {
          newMap.get(chatId).delete(userId);
          if (newMap.get(chatId).size === 0) {
            newMap.delete(chatId);
          }
        }
        return newMap;
      });
    });

    // Error handling
    socket.on('error', (error) => {
      console.error('❌ Socket error:', error);
      toast.error(error.message || 'Socket error occurred');
    });

    // Cleanup on unmount
    return () => {
      console.log('🧹 Cleaning up socket connection');

      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }

      if (socket.connected) {
        socket.disconnect();
      }

      setIsConnected(false);
      setIsConnecting(false);
      setOnlineUsers(new Set());
      setTypingUsers(new Map());
      eventListenersRef.current.clear();
      pendingOperationsRef.current = [];
      pendingListenersRef.current = [];
    };
  }, [isAuthenticated, authToken, user?.uid, isConnecting]); // Include isConnecting in dependencies

  // Internal event listener function (synchronous)
  const addEventListenerInternal = useCallback((event, callback) => {
    const socket = socketRef.current;
    if (!socket) {
      throw new Error('Socket not available');
    }

    // Create a unique key for this event-callback pair
    const eventKey = `${event}_${Date.now()}_${Math.random()}`;

    // Add new listener
    socket.on(event, callback);
    eventListenersRef.current.set(eventKey, { event, callback });

    // Return cleanup function
    return () => {
      const socket = socketRef.current;
      if (socket && eventListenersRef.current.has(eventKey)) {
        const { event: storedEvent, callback: storedCallback } = eventListenersRef.current.get(eventKey);
        socket.off(storedEvent, storedCallback);
        eventListenersRef.current.delete(eventKey);
      }
    };
  }, []);

  // Generic event listener management - stable reference with queuing
  const addEventListener = useCallback((event, callback) => {
    const socket = socketRef.current;

    // If socket is available and connected, add listener immediately
    if (socket && isConnected) {
      return addEventListenerInternal(event, callback);
    }

    // If socket is not available or not connected, queue the listener
    console.log('🔄 Queueing event listener for:', event);
    return new Promise((resolve) => {
      pendingListenersRef.current.push({ event, callback, resolve });
    });
  }, [isConnected, addEventListenerInternal]); // Include isConnected in dependencies

  const removeEventListener = useCallback((event) => {
    if (!socketRef.current) return;

    if (eventListenersRef.current.has(event)) {
      socketRef.current.off(event, eventListenersRef.current.get(event));
      eventListenersRef.current.delete(event);
    }
  }, []);

  // Socket emission methods with queuing
  const emit = useCallback((event, data) => {
    const socket = socketRef.current;

    // If socket is connected, emit immediately
    if (socket && socket.connected) {
      try {
        socket.emit(event, data);
        return Promise.resolve(true);
      } catch (error) {
        console.error('❌ Socket emit error:', error);
        return Promise.resolve(false);
      }
    }

    // If socket is connecting, queue the operation
    if (isConnecting) {
      console.log('🔄 Queueing socket emit for:', event);
      return new Promise((resolve) => {
        const operation = () => {
          const socket = socketRef.current;
          if (socket && socket.connected) {
            try {
              socket.emit(event, data);
              return true;
            } catch (error) {
              console.error('❌ Socket emit error:', error);
              return false;
            }
          }
          return false;
        };
        pendingOperationsRef.current.push({ operation, resolve });
      });
    }

    // Socket not available and not connecting
    console.warn('⚠️ Socket not connected, cannot emit:', event);
    return Promise.resolve(false);
  }, [isConnecting]); // Include isConnecting in dependencies

  // Chat-specific methods
  const joinChat = useCallback((chatId) => {
    return emit('chat:join', { chatId });
  }, [emit]);

  const leaveChat = useCallback((chatId) => {
    return emit('chat:leave', { chatId });
  }, [emit]);

  const sendMessage = useCallback((chatId, text, replyTo = null) => {
    return emit('message:send', { chatId, text, replyTo });
  }, [emit]);

  const markMessagesSeen = useCallback((chatId, messageIds) => {
    return emit('message:seen', { chatId, messageIds });
  }, [emit]);

  const startTyping = useCallback((chatId) => {
    return emit('typing:start', { chatId });
  }, [emit]);

  const stopTyping = useCallback((chatId) => {
    return emit('typing:stop', { chatId });
  }, [emit]);

  // Helper methods - use refs to avoid dependency on state
  const onlineUsersRef = useRef(onlineUsers);
  const typingUsersRef = useRef(typingUsers);

  // Update refs when state changes
  useEffect(() => {
    onlineUsersRef.current = onlineUsers;
  }, [onlineUsers]);

  useEffect(() => {
    typingUsersRef.current = typingUsers;
  }, [typingUsers]);

  const isUserOnline = useCallback((userId) => {
    return onlineUsersRef.current.has(userId);
  }, []); // No dependencies - use ref

  const getTypingUsersInChat = useCallback((chatId) => {
    return Array.from(typingUsersRef.current.get(chatId) || []);
  }, []); // No dependencies - use ref

  const isUserTypingInChat = useCallback((chatId, userId) => {
    return typingUsersRef.current.get(chatId)?.has(userId) || false;
  }, []); // No dependencies - use ref

  const value = {
    // Connection state
    socket: socketRef.current,
    isConnected,
    isConnecting,

    // User status
    onlineUsers,
    isUserOnline,

    // Typing status
    typingUsers,
    getTypingUsersInChat,
    isUserTypingInChat,

    // Event management
    addEventListener,
    removeEventListener,
    emit,

    // Chat methods
    joinChat,
    leaveChat,
    sendMessage,
    markMessagesSeen,
    startTyping,
    stopTyping,
  };

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  );
};
